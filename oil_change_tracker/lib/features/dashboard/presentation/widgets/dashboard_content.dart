import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/models/car_model.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../generated/app_localizations.dart';
import '../../../car_management/providers/car_provider.dart';
import '../screens/dashboard_screen.dart';

class DashboardContent extends ConsumerWidget {
  final List<CarModel> cars;
  final String userName;

  const DashboardContent({
    super.key,
    required this.cars,
    required this.userName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGreeting(context),
          const SizedBox(height: 24),
          if (cars.isEmpty)
            _buildNoCarsView(context)
          else ...[
            _buildOilConsumptionGauge(context),
            const SizedBox(height: 24),
            _buildCarsList(context),
            const SizedBox(height: 24),
            _buildMaintenanceAlerts(context),
          ],
          const SizedBox(height: 24),
          _buildQuickActions(context),
        ],
      ),
    );
  }

  Widget _buildGreeting(BuildContext context) {
    final now = DateTime.now();
    String greeting;
    final l10n = S.of(context);
    
    if (now.hour < 12) {
      greeting = l10n.goodMorning;
    } else if (now.hour < 17) {
      greeting = l10n.goodAfternoon;
    } else {
      greeting = l10n.goodEvening;
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$greeting, ${userName.isNotEmpty ? userName : l10n.unknownUser}',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n.welcomeBack,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildOilConsumptionGauge(BuildContext context) {
    final l10n = S.of(context);
    
    // Find the car with the highest oil consumption percentage
    CarModel? mostDueCar;
    double highestProgress = 0;
    
    for (final car in cars) {
      final progress = car.oilChangeProgress;
      if (progress > highestProgress) {
        highestProgress = progress;
        mostDueCar = car;
      }
    }
    
    if (mostDueCar == null) {
      return const SizedBox.shrink();
    }
    
    final progress = mostDueCar.oilChangeProgress;
    final isOverdue = progress >= 1.0;
    final color = isOverdue ? context.secondaryAccentColor : context.accentColor;
    final percentage = (progress * 100).toInt();
    
    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: color, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.oil_barrel, color: context.accentColor),
                const SizedBox(width: 8),
                Text(
                  l10n.oilConsumption,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.accentColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                CircularPercentIndicator(
                  radius: 60.0,
                  lineWidth: 10.0,
                  percent: progress > 1.0 ? 1.0 : progress,
                  center: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '$percentage%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18.0,
                          color: color,
                        ),
                      ),
                      Text(
                        l10n.used,
                        style: TextStyle(
                          fontSize: 12.0,
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  progressColor: color,
                  backgroundColor: color.withOpacity(0.2),
                  circularStrokeCap: CircularStrokeCap.round,
                  animation: true,
                  animationDuration: 1200,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${mostDueCar.year} ${mostDueCar.make} ${mostDueCar.model}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isOverdue 
                            ? l10n.oilChangeOverdue
                            : l10n.kmUntilNextOilChange(mostDueCar.kilometersUntilNextChange),
                        style: TextStyle(
                          color: isOverdue ? context.secondaryAccentColor : context.accentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () => context.push('/oil-changes/add'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: color,
                          foregroundColor: context.containerBackgroundColor,
                          minimumSize: const Size(120, 36),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(l10n.recordOilChange),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoCarsView(BuildContext context) {
    final l10n = S.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              l10n.welcomeText,
              style: TextStyle(
                color: context.accentColor,
                fontSize: 24,
                fontWeight: FontWeight.bold
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noCarsFound,
              style: TextStyle(
                color: context.secondaryTextColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            PrimaryButton(
              text: l10n.addCar,
              onPressed: () => context.push('/cars/add'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarsList(BuildContext context) {
    final l10n = S.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.myCars,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...cars.map((car) => _buildCarCard(context, car)),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    final l10n = S.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.quickActions,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: SizedBox(
            height: 120, // Fixed height to ensure consistent sizing
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: _buildActionButton(
                    context,
                    icon: Icons.directions_car,
                    label: l10n.myCars,
                    onTap: () => context.push('/cars'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: _buildActionButton(
                    context,
                    icon: Icons.oil_barrel,
                    label: l10n.recordOilChange,
                    onTap: () {
                      if (cars.isNotEmpty) {
                        context.push('/oil-changes/add');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.addCarFirst)),
                        );
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: _buildActionButton(
                    context,
                    icon: Icons.build,
                    label: l10n.recordMaintenance,
                    onTap: () {
                      if (cars.isNotEmpty) {
                        context.push('/maintenance/add');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.addCarFirst)),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: SizedBox(
            height: 120, // Fixed height to ensure consistent sizing
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: _buildActionButton(
                    context,
                    icon: Icons.settings,
                    label: l10n.settings,
                    onTap: () => context.push('/settings'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(flex: 1, child: SizedBox()),
                const SizedBox(width: 16),
                Expanded(flex: 1, child: SizedBox()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: context.accentColor, size: 28),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    color: context.accentColor,
                    fontSize: 13,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCarCard(BuildContext context, CarModel car) {
    final l10n = S.of(context);
    final isOverdue = car.isOilChangeDue;
    
    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isOverdue ? context.secondaryAccentColor : context.accentColor, 
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => context.push('/cars/${car.id}'),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: ClipOval(
                  child: Image.asset(
                    'assets/images/app_icon.png',
                    width: 36,
                    height: 36,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${car.year} ${car.make} ${car.model}',
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.speed,
                          color: context.secondaryTextColor,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${NumberFormat.decimalPattern().format(car.currentMileage)} km',
                          style: TextStyle(color: context.secondaryTextColor),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isOverdue ? context.secondaryAccentColor : context.accentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isOverdue 
                            ? 'Oil Change Overdue' 
                            : 'Next Oil Change: ${car.mileageUntilDue} km',
                        style: TextStyle(
                          color: isOverdue 
                              ? Colors.white
                              : context.isDarkMode ? Colors.black : Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.chevron_right, color: context.accentColor),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMaintenanceAlerts(BuildContext context) {
    final l10n = S.of(context);
    final alerts = cars.where((car) => car.isOilChangeDue).toList();
    
    if (alerts.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.notifications,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...alerts.map((car) => _buildAlertCard(context, car)),
      ],
    );
  }

  Widget _buildAlertCard(BuildContext context, CarModel car) {
    final l10n = S.of(context);
    
    return Card(
      color: context.containerBackgroundColor,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: context.secondaryAccentColor, width: 1),
      ),
      child: InkWell(
        onTap: () => context.push('/oil-changes/add'),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.secondaryAccentColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.warning,
                  color: context.secondaryAccentColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${car.year} ${car.make} ${car.model}',
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Oil Change Overdue',
                      style: TextStyle(color: context.secondaryAccentColor),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.speed,
                          color: context.secondaryTextColor,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${NumberFormat.decimalPattern().format(car.currentMileage)} km',
                          style: TextStyle(color: context.secondaryTextColor),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              PrimaryButton(
                text: l10n.recordOilChange,
                onPressed: () => context.push('/oil-changes/add'),
                fullWidth: false,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUpdateMileageDialog(BuildContext context, WidgetRef ref) {
    // Delegate to the centralized implementation in dashboard_screen.dart
    // Get the currently selected car (first car if available)
    if (cars.isNotEmpty) {
      final currentCar = cars.first;
      // Use the shared implementation
      showMileageUpdateDialog(context, ref, currentCar);
    } else {
      // No cars available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).addCarFirst),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}