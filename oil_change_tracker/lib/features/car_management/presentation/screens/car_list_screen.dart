import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import '../../providers/car_provider.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../../shared/widgets/offline_error_text.dart';
import '../../../../core/services/connectivity_service.dart';
import '../../../../shared/services/image_cache_service.dart';
import 'dart:developer' as dev;
import '../../../../shared/widgets/image_carousel.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/widgets/unified_app_bar.dart';
import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';

class CarListScreen extends ConsumerWidget {
  const CarListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final isConnected = ref.watch(isConnectedProvider);
    final carsAsync = ref.watch(carsProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBarFactory.standard(
        title: l10n.myCars,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(carsProvider);
        },
        color: context.accentColor,
        child: carsAsync.when(
        data: (cars) {
          if (cars.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    l10n.noCarsFound,
                    style: TextStyle(color: context.primaryTextColor),
                  ),
                  const SizedBox(height: 24),
                  PrimaryButton(
                    text: l10n.addCar,
                    onPressed: () {
                      if (isConnected) {
                        _handleAddCar(context, ref, cars);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(l10n.offlineMode),
                            backgroundColor: context.secondaryAccentColor,
                          ),
                        );
                      }
                    },
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            itemCount: cars.length,
            itemBuilder: (context, index) {
              final car = cars[index];
              return _buildCarCard(context, car, isConnected);
            },
          );
        },
        loading: () => Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: OfflineErrorText(
              errorMessage: error.toString(),
              onRetry: () => ref.refresh(carsProvider),
            ),
          ),
        ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (isConnected) {
            carsAsync.whenData((cars) {
              _handleAddCar(context, ref, cars);
            });
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(l10n.offlineMode),
                backgroundColor: context.secondaryAccentColor,
              ),
            );
          }
        },
        backgroundColor: context.accentColor,
        child: Icon(Icons.add, color: context.containerBackgroundColor),
      ),
    );
  }

  void _handleAddCar(BuildContext context, WidgetRef ref, List<CarModel> cars) {
    // Check if the user has reached the vehicle limit
    final hasUnlimitedVehicles =
        ref.read(featureGateProvider(PremiumFeature.unlimitedVehicles));

    // Free users are limited to 3 vehicles
    if (!hasUnlimitedVehicles && cars.length >= 3) {
      _showSubscriptionPromotion(context);
    } else {
      context.push('/cars/add');
    }
  }

  // Show subscription promotion dialog
  void _showSubscriptionPromotion(BuildContext context) {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.unlimitedVehicles),
            const SizedBox(height: 16),
            Text(
                'Free users are limited to 3 vehicles. Upgrade to Premium for unlimited vehicles.'),
            const SizedBox(height: 8),
            // Use hardcoded features since we don't have specific localization keys for each feature
            ...[
              'Voice input for quick data entry',
              'Ad-free experience',
              'Unlimited vehicles',
              'Enhanced analytics and insights',
              'Cloud backup and sync'
            ].map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(feature)),
                    ],
                  ),
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }

  Widget _buildCarCard(BuildContext context, CarModel car, bool isConnected) {
    final isOverdue = car.isOilChangeDue;
    final l10n = S.of(context);
    final imageCacheService = ImageCacheService();

    // Validate and collect all valid image URLs
    List<String> validImageUrls = [];

    // First check if car has multiple images
    if (car.imageUrls != null && car.imageUrls!.isNotEmpty) {
      for (String url in car.imageUrls!) {
        String? validUrl = imageCacheService.validateAndFixUrl(url);
        if (validUrl != null) {
          validImageUrls.add(validUrl);
        }
      }
    }
    // If no valid URLs in imageUrls array, try the single imageUrl
    else if (validImageUrls.isEmpty &&
        car.imageUrl != null &&
        car.imageUrl!.isNotEmpty) {
      String? validUrl = imageCacheService.validateAndFixUrl(car.imageUrl);
      if (validUrl != null) {
        validImageUrls.add(validUrl);
      }
    }

    if (validImageUrls.isEmpty) {
      dev.log('No valid images found for car: ${car.id}');
    } else {
      dev.log('Found ${validImageUrls.length} valid images for car: ${car.id}');
    }

    return Card(
      color: context.containerBackgroundColor,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isOverdue ? context.secondaryAccentColor : context.accentColor,
          width: isOverdue ? 1.5 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => context.push('/cars/${car.id}'),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car images if available
            if (validImageUrls.isNotEmpty)
              ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(15)),
                child: Stack(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: 150,
                      child: ImageCarousel(
                        networkImages: validImageUrls,
                        height: 150,
                        enableAdd: false,
                        enableDelete: false,
                        showIndicator: validImageUrls.length > 1,
                        autoPlay: validImageUrls.length > 1,
                        imageFit: BoxFit.cover,
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(15)),
                      ),
                    ),
                    // Gradient overlay for text readability
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.bottomCenter,
                            end: Alignment.topCenter,
                            colors: [
                              Colors.black.withOpacity(0.8),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Car info on the image
                    Positioned(
                      bottom: 8,
                      left: 16,
                      right: 16,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${car.year} ${car.make} ${car.model}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                shadows: [
                                  Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 3.0,
                                    color: Colors.black,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              Icon(
                                Icons.speed,
                                color: context.accentColor,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${car.currentMileage} km',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  shadows: [
                                    Shadow(
                                      offset: Offset(0, 1),
                                      blurRadius: 3.0,
                                      color: Colors.black,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Car info header - only show if no image
                  if (validImageUrls.isEmpty)
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: context.accentColor.withOpacity(0.1),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isOverdue
                                  ? AppColors.burgundy
                                  : context.accentColor,
                              width: 1.5,
                            ),
                          ),
                          child: Icon(
                            Icons.directions_car,
                            color: context.accentColor,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${car.year} ${car.make} ${car.model}',
                                style: TextStyle(
                                  color: context.primaryTextColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.speed,
                                    color: context.accentColor,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${car.currentMileage} km',
                                    style: TextStyle(
                                      color: context.secondaryTextColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (!isConnected)
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: context.accentColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Tooltip(
                              message: 'Available offline',
                              child: Icon(
                                Icons.offline_pin,
                                color: context.accentColor,
                                size: 18,
                              ),
                            ),
                          ),
                      ],
                    ),

                  if (validImageUrls.isEmpty)
                    const SizedBox(height: 16)
                  else
                    const SizedBox(height: 8),

                  // Oil change status indicator
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isOverdue
                          ? AppColors.burgundy.withOpacity(0.2)
                          : context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isOverdue
                            ? AppColors.burgundy
                            : context.accentColor.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isOverdue ? Icons.warning_amber : Icons.oil_barrel,
                          color: isOverdue
                              ? AppColors.burgundy
                              : context.accentColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isOverdue
                                    ? l10n.oilChangeOverdue
                                    : l10n.oilChangeStatus,
                                style: TextStyle(
                                  color: isOverdue
                                      ? AppColors.burgundy
                                      : context.accentColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                isOverdue
                                    ? l10n.oilChangeOverdue
                                    : l10n.kmUntilNextOilChange(
                                        car.kilometersUntilNextChange),
                                style: TextStyle(
                                  color: isOverdue
                                      ? AppColors.burgundy.withOpacity(0.8)
                                      : context.secondaryTextColor,
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (!isOverdue)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: context.accentColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${car.kilometersUntilNextChange} km',
                              style: TextStyle(
                                color: context.accentColor,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // View details button
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      onPressed: () => context.push('/cars/${car.id}'),
                      icon: Icon(
                        Icons.arrow_forward,
                        color: context.secondaryAccentColor,
                        size: 16,
                      ),
                      label: Text(
                        l10n.details,
                        style: TextStyle(
                          color: context.secondaryAccentColor,
                          fontSize: 13,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        minimumSize: Size.zero,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
