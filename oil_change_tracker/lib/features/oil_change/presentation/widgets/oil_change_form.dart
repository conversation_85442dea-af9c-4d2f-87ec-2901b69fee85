import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:oil_change_tracker/features/oil_change/data/repositories/oil_change_repository.dart';
import 'package:oil_change_tracker/features/oil_change/domain/models/oil_change_model.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/theme/theme_extensions.dart';

class OilChangeForm extends ConsumerStatefulWidget {
  final String carId;
  final OilChangeModel? oilChange;

  const OilChangeForm({
    super.key,
    required this.carId,
    this.oilChange,
  });

  @override
  ConsumerState<OilChangeForm> createState() => _OilChangeFormState();
}

class _OilChangeFormState extends ConsumerState<OilChangeForm> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _date;
  late TextEditingController _mileageController;
  late TextEditingController _oilTypeController;
  late TextEditingController _oilEnduranceController;
  late TextEditingController _filterTypeController;
  late TextEditingController _notesController;

  // Common oil types for suggestions
  final List<String> _commonOilTypes = [
    '5W-30',
    '5W-40',
    '10W-30',
    '10W-40',
    '0W-20',
    '0W-30',
    '15W-40',
    '20W-50'
  ];

  // Common filter types for suggestions
  final List<String> _commonFilterTypes = [
    'OEM Filter',
    'Cartridge Filter',
    'Spin-on Filter',
    'Premium Filter',
    'Standard Filter',
    'High Performance Filter'
  ];

  @override
  void initState() {
    super.initState();
    _date = widget.oilChange?.date ?? DateTime.now();
    _mileageController = TextEditingController(
      text: widget.oilChange?.mileage.toString() ?? '',
    );
    _oilTypeController = TextEditingController(
      text: widget.oilChange?.oilType ?? '',
    );
    _oilEnduranceController = TextEditingController(
      text: widget.oilChange?.oilEnduranceKm.toString() ?? '',
    );
    _filterTypeController = TextEditingController(
      text: widget.oilChange?.filterType ?? '',
    );
    _notesController = TextEditingController(
      text: widget.oilChange?.notes ?? '',
    );
  }

  @override
  void dispose() {
    _mileageController.dispose();
    _oilTypeController.dispose();
    _oilEnduranceController.dispose();
    _filterTypeController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final l10n = S.of(context);
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _date,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: context.accentColor,
              onPrimary: context.primaryTextColor,
              surface: context.containerBackgroundColor,
              onSurface: context.primaryTextColor,
            ),
            dialogTheme: DialogTheme(
              backgroundColor: context.containerBackgroundColor,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _date) {
      setState(() {
        _date = picked;
      });
    }
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int? maxLines,
    List<String>? suggestions,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (suggestions != null && suggestions.isNotEmpty)
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: suggestions.map((suggestion) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ActionChip(
                      backgroundColor: context.containerBackgroundColor,
                      side: BorderSide(color: context.accentColor),
                      label: Text(
                        suggestion,
                        style: TextStyle(color: context.accentColor),
                      ),
                      onPressed: () {
                        controller.text = suggestion;
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines ?? 1,
            style: TextStyle(color: context.primaryTextColor),
            decoration: InputDecoration(
              labelText: label,
              labelStyle: TextStyle(
                color: context.accentColor.withOpacity(0.7),
                fontSize: 16,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: context.accentColor.withOpacity(0.5),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: context.secondaryAccentColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade300,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.red.shade300,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: context.containerBackgroundColor.withOpacity(0.3),
            ),
            validator: validator,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                widget.oilChange == null ? l10n.recordOilChange : l10n.update,
                style: TextStyle(
                  color: context.accentColor,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              InkWell(
                onTap: () => _selectDate(context),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.containerBackgroundColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.5),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.date,
                            style: TextStyle(
                              color: context.accentColor.withOpacity(0.7),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat.yMMMd().format(_date),
                            style: TextStyle(
                              color: context.primaryTextColor,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      Icon(
                        Icons.calendar_today,
                        color: context.accentColor,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              _buildInputField(
                controller: _mileageController,
                label: l10n.mileage,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterMileage;
                  }
                  if (int.tryParse(value) == null) {
                    return l10n.invalidMileage;
                  }
                  return null;
                },
              ),
              _buildInputField(
                controller: _oilTypeController,
                label: l10n.oilType,
                suggestions: _commonOilTypes,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterOilType;
                  }
                  return null;
                },
              ),
              _buildInputField(
                controller: _oilEnduranceController,
                label: l10n.oilEnduranceKm,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterOilEndurance;
                  }
                  if (int.tryParse(value) == null) {
                    return l10n.invalidMileage;
                  }
                  return null;
                },
              ),
              _buildInputField(
                controller: _filterTypeController,
                label: l10n.filterType,
                suggestions: _commonFilterTypes,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterFilterType;
                  }
                  return null;
                },
              ),
              _buildInputField(
                controller: _notesController,
                label: l10n.notes,
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.secondaryAccentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
                child: Text(
                  l10n.save,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    final l10n = S.of(context);
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      final oilChange = OilChangeModel(
        id: widget.oilChange?.id ?? '',
        carId: widget.carId,
        userId: user.uid,
        date: _date,
        mileage: int.parse(_mileageController.text),
        oilType: _oilTypeController.text,
        oilEnduranceKm: int.parse(_oilEnduranceController.text),
        filterType: _filterTypeController.text,
        notes: _notesController.text,
        createdAt: widget.oilChange?.createdAt ?? DateTime.now(),
      );

      if (widget.oilChange == null) {
        await ref
            .read(oilChangeRepositoryProvider(widget.carId).notifier)
            .addOilChange(oilChange);
      } else {
        await ref
            .read(oilChangeRepositoryProvider(widget.carId).notifier)
            .updateOilChange(oilChange);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.oilChange == null ? l10n.saveSuccess : l10n.saveSuccess,
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n.error}: ${e.toString()}'),
            backgroundColor: context.secondaryAccentColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}
